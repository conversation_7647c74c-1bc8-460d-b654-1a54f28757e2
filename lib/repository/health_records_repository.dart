import 'package:drift/drift.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../providers/database_provider.dart';
import '../types/health_record.dart';
import '../types/health_types.dart';
import 'database.dart';

part 'health_records_repository.g.dart';

/// 记录信息辅助类
class _RecordInfo {
  final int id;
  final DateTime createdAt;
  final HealthRecordTypeEnum type;

  const _RecordInfo({
    required this.id,
    required this.createdAt,
    required this.type,
  });
}

/// 健康记录仓库 - 用于分页查询所有健康记录
class HealthRecordsRepository {
  final HealthDiaryDatabase _database;

  HealthRecordsRepository(this._database);

  /// 分页获取所有健康记录（血压和血糖）
  Future<List<HealthRecordEntry>> getHealthRecordsPaginated({
    required int page,
    required int pageSize,
  }) async {
    // 血糖查询
    final sugarQuery = _database.selectOnly(_database.bloodSugars)
      ..addColumns([
        _database.bloodSugars.id,
        _database.bloodSugars.createdAt,
        CustomExpression<int>(HealthRecordTypeEnum.bloodSugar.index.toString())
      ]);

    // 血压查询
    final pressureQuery = _database.selectOnly(_database.bloodPressures)
      ..addColumns([
        _database.bloodPressures.id,
        _database.bloodPressures.createdAt,
        CustomExpression<int>(HealthRecordTypeEnum.bloodPressure.index.toString())
      ]);

    // 合并查询并分页
    final query = pressureQuery
        ..unionAll(sugarQuery)
        ..orderBy([OrderingTerm.desc(_database.bloodPressures.createdAt)])
        ..limit(pageSize, offset: (page - 1) * pageSize);

    final queryResults = await query.get();

    // 解析查询结果，按类型分组ID
    final recordInfoList = <_RecordInfo>[];
    final bloodPressureIds = <int>[];
    final bloodSugarIds = <int>[];

    for (final row in queryResults) {
      final id = row.read<int>(_database.bloodPressures.id);
      final createdAt = row.read<DateTime>(_database.bloodPressures.createdAt);
      final typeIndex = row.rawData.read<int>('c2');
      final recordType = HealthRecordTypeEnum.fromInt(typeIndex);

      if (id == null || createdAt == null) {
        continue;
      }

      recordInfoList.add(_RecordInfo(
        id: id,
        createdAt: createdAt,
        type: recordType,
      ));


      switch (recordType) {
        case HealthRecordTypeEnum.bloodPressure:
          bloodPressureIds.add(id);
          break;
        case HealthRecordTypeEnum.bloodSugar:
          bloodSugarIds.add(id);
          break;
      }
    }

    // 批量查询完整记录
    final bloodPressureRecordsMap = <int, BloodPressure>{};
    final bloodSugarRecordsMap = <int, BloodSugar>{};

    // 批量查询血压记录
    if (bloodPressureIds.isNotEmpty) {
      final records = await _getBatchBloodPressureRecords(bloodPressureIds);
      for (final record in records) {
        bloodPressureRecordsMap[record.id] = record;
      }
    }

    // 批量查询血糖记录
    if (bloodSugarIds.isNotEmpty) {
      final records = await _getBatchBloodSugarRecords(bloodSugarIds);
      for (final record in records) {
        bloodSugarRecordsMap[record.id] = record;
      }
    }

    // 按原始顺序转换记录
    final healthRecords = <HealthRecordEntry>[];
    for (final info in recordInfoList) {
      switch (info.type) {
        case HealthRecordTypeEnum.bloodPressure:
          final record = bloodPressureRecordsMap[info.id];
          if (record != null) {
            healthRecords.add(_convertBloodPressureToHealthRecord(record));
          }
          break;
        case HealthRecordTypeEnum.bloodSugar:
          final record = bloodSugarRecordsMap[info.id];
          if (record != null) {
            healthRecords.add(_convertBloodSugarToHealthRecord(record));
          }
          break;
      }
    }

    return healthRecords;
  }

  /// 批量获取血压记录
  Future<List<BloodPressure>> _getBatchBloodPressureRecords(List<int> ids) async {
    if (ids.isEmpty) return [];

    return await (_database.select(_database.bloodPressures)
      ..where((t) => t.id.isIn(ids))).get();
  }

  /// 批量获取血糖记录
  Future<List<BloodSugar>> _getBatchBloodSugarRecords(List<int> ids) async {
    if (ids.isEmpty) return [];

    return await (_database.select(_database.bloodSugars)
      ..where((t) => t.id.isIn(ids))).get();
  }

  /// 将血压记录转换为健康记录条目
  HealthRecordEntry _convertBloodPressureToHealthRecord(BloodPressure record) {
    return HealthRecordEntry(
      time: _formatDateTime(record.createdAt),
      type: 'blood_pressure'.tr(),
      note: record.note ?? '',
      recordType: HealthRecordTypeEnum.bloodPressure,
      color: 0xFFFFEBF0,
      values: [
        HealthRecordValue(
          label: 'systolic'.tr(),
          value: record.systolic.toString(),
          unit: 'mmHg',
        ),
        HealthRecordValue(
          label: 'diastolic'.tr(),
          value: record.diastolic.toString(),
          unit: 'mmHg',
        ),
        if (record.pulse != null)
          HealthRecordValue(
            label: 'pulse'.tr(),
            value: record.pulse.toString(),
            unit: 'bpm',
          ),
      ],
    );
  }

  /// 将血糖记录转换为健康记录条目
  HealthRecordEntry _convertBloodSugarToHealthRecord(BloodSugar record) {
    return HealthRecordEntry(
      time: _formatDateTime(record.createdAt),
      type: 'blood_sugar'.tr(),
      note: record.note ?? '',
      recordType: HealthRecordTypeEnum.bloodSugar,
      color: 0xFFE4F4FF,
      values: [
        HealthRecordValue(
          label: 'blood_sugar'.tr(),
          value: record.value.toStringAsFixed(1),
          unit: 'mmol/L',
        ),
      ],
    );
  }

  /// 格式化日期时间
  String _formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = today.subtract(const Duration(days: 1));
    final recordDate = DateTime(dateTime.year, dateTime.month, dateTime.day);

    final timeFormat = DateFormat('HH:mm');
    final timeString = timeFormat.format(dateTime);

    if (recordDate == today) {
      return '${'today'.tr()} $timeString';
    } else if (recordDate == yesterday) {
      return '${'yesterday'.tr()} $timeString';
    } else {
      final dateFormat = DateFormat('MM-dd');
      return '${dateFormat.format(dateTime)} $timeString';
    }
  }
}

/// 健康记录仓库提供者
@riverpod
HealthRecordsRepository healthRecordsRepository(Ref ref) {
  final database = ref.watch(databaseProvider);
  return HealthRecordsRepository(database);
}
