import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';

import '../../routers/app_router.dart';
import '../../themes/app_theme.dart';
import '../home/<USER>';
import '../settings/settings_page.dart';

class MainFramePage extends ConsumerStatefulWidget {
  const MainFramePage({super.key});

  @override
  ConsumerState<MainFramePage> createState() => _MainFramePageState();
}

class _MainFramePageState extends ConsumerState<MainFramePage> {
  int _selectedIndex = 0;

  static const List<Widget> _widgetOptions = <Widget>[HomePage(), SettingsPage()];

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(index: _selectedIndex, children: _widgetOptions),
      bottomNavigationBar: BottomAppBar(
        color: Colors.white,
        surfaceTintColor: Colors.white,
        elevation: 8.0,
        height: 64,
        padding: EdgeInsets.zero,
        shape: const CircularNotchedRectangle(),
        notchMargin: 8.0,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: <Widget>[
            Expanded(
              child: IconButton(
                icon: const FaIcon(FontAwesomeIcons.house, size: 20),
                color: _selectedIndex == 0
                    ? context.appColors.bottomNavigationBarIconSelected
                    : context.appColors.bottomNavigationBarIcon,
                onPressed: () {
                  _onItemTapped(0);
                },
              ),
            ),
            ElevatedButton(
              onPressed: () {
                const AddRecordRoute(type: 'blood-sugar').go(context);
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).primaryColor,
                shape: const CircleBorder(),
              ),
              child: Center(
                child: FaIcon(FontAwesomeIcons.plus, size: 32, color: Theme.of(context).scaffoldBackgroundColor),
              ),
            ),
            Expanded(
              child: IconButton(
                icon: const FaIcon(FontAwesomeIcons.gear, size: 20),
                color: _selectedIndex == 1
                    ? context.appColors.bottomNavigationBarIconSelected
                    : context.appColors.bottomNavigationBarIcon,
                onPressed: () {
                  _onItemTapped(1);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

}
